import React from "react";
import { AdminWrapper } from "../../../components/AdminWrapper";
import DashboardStatCard from "../../../components/DashboardStatCard";
import { MkdSimpleTable } from "../../../components/MkdSimpleTable";
import {
  UsersIcon,
  ListingsIcon,
  DisputesAndRefundsIcon,
} from "@/assets/svgs/AdminDashboard";
import { useAdminDashboard } from "@/hooks/useAdminDashboard";
import { Skeleton } from "@/components/Skeleton";
import { format } from "date-fns";

const AdminDashboardPage = () => {
  const { data, isLoading } = useAdminDashboard();

  const recentTransactionsColumns = [
    {
      header: "",
      accessor: "transaction",
    },
    {
      header: "",
      accessor: "amount",
    },
  ];

  const recentApplicationsColumns = [
    {
      header: "",
      accessor: "applicant",
    },
    {
      header: "",
      accessor: "status",
    },
  ];

  const recentTransactionsData =
    data?.recent_transactions.map((item: any) => {
      const userData = JSON.parse(item.user_data || "{}");
      return {
        transaction: (
          <div className="flex items-center py-2">
            <img
              src={userData.photo || "https://i.pravatar.cc/40"}
              alt="User"
              className="h-10 w-10 rounded-full"
            />
            <div className="ml-3">
              <p className="font-semibold text-gray-900">
                {userData.name || "John Doe"}
              </p>
              <p className="text-sm text-gray-500">
                Purchase #{item.id || "12345"}
              </p>
            </div>
          </div>
        ),
        amount: (
          <div className="text-right">
            <p className="font-semibold text-red-500">
              eBa$ {item.amount || "209.99"}
            </p>
          </div>
        ),
      };
    }) || [];

  const recentApplicationsData =
    data?.recent_applications.map((item: any) => {
      const userData = JSON.parse(item.user_data || "{}");
      return {
        applicant: (
          <div className="flex items-center py-2">
            <img
              src={userData.photo || "https://i.pravatar.cc/40"}
              alt="User"
              className="h-10 w-10 rounded-full"
            />
            <div className="ml-3">
              <p className="font-semibold text-gray-900">
                {userData.name || "John Doe"}
              </p>
              <p className="text-sm text-gray-500">Delivery Agent</p>
            </div>
          </div>
        ),
        status: (
          <div className="text-right">
            <span className="inline-flex items-center rounded-full bg-green-100 px-3 py-1 text-sm font-medium text-green-800">
              {item.status || "Pending"}
            </span>
          </div>
        ),
      };
    }) || [];

  return (
    <AdminWrapper>
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
          {isLoading ? (
            <>
              <Skeleton className="h-24 w-full" />
              <Skeleton className="h-24 w-full" />
              <Skeleton className="h-24 w-full" />
            </>
          ) : (
            <>
              <DashboardStatCard
                title="Total Users"
                value={data?.stats?.total_users || 0}
                icon={<UsersIcon className="text-blue-500" />}
              />
              <DashboardStatCard
                title="Active Listings"
                value={data?.stats?.active_listings || 0}
                icon={<ListingsIcon className="text-green-500" />}
              />
              <DashboardStatCard
                title="Pending Disputes"
                value={data?.stats?.pending_disputes || 0}
                icon={<DisputesAndRefundsIcon className="text-red-500" />}
              />
            </>
          )}
        </div>

        <div className="mt-8 grid grid-cols-1 gap-6 lg:grid-cols-3">
          <div className="rounded-lg bg-white p-6 shadow">
            <h3 className="text-lg font-semibold">Revenue Breakdown</h3>
            {isLoading ? (
              <Skeleton className="mt-4 h-48 w-full" />
            ) : (
              <ul className="mt-4 space-y-2">
                <li className="flex justify-between">
                  <span>USD Fees</span>
                  <span className="font-semibold">
                    USD$ {data?.revenue?.usd_fees || 0}
                  </span>
                </li>
                <li className="flex justify-between">
                  <span>eBa$ Fees</span>
                  <span className="font-semibold text-red-500">
                    eBa$ {data?.revenue?.ebas_fees || 0}
                  </span>
                </li>
                <li className="flex justify-between">
                  <span>Transaction Fees</span>
                  <span className="font-semibold">
                    USD$ {data?.revenue?.transaction_fees || 0}
                  </span>
                </li>
                <li className="flex justify-between">
                  <span>Credit Balance Fees</span>
                  <span className="font-semibold">
                    USD$ {data?.revenue?.credit_balance_fees || 0}
                  </span>
                </li>
                <li className="flex justify-between">
                  <span>FX Transaction Fees</span>
                  <span className="font-semibold">
                    USD$ {data?.revenue?.fx_transaction_fees || 0}
                  </span>
                </li>
              </ul>
            )}
          </div>
          <div className="rounded-lg bg-white p-6 shadow">
            <h3 className="text-lg font-semibold">Expenses</h3>
            {isLoading ? (
              <Skeleton className="mt-4 h-48 w-full" />
            ) : (
              <ul className="mt-4 space-y-2">
                <li className="flex justify-between">
                  <span>Loyalty Rewards Paid</span>
                  <span className="font-semibold text-red-500">
                    eBa$ {data?.expenses?.loyalty_rewards_paid || 0}
                  </span>
                </li>
                <li className="flex justify-between">
                  <span>Referral Commissions</span>
                  <span className="font-semibold text-red-500">
                    eBa$ {data?.expenses?.referral_commissions || 0}
                  </span>
                </li>
                <li className="flex justify-between">
                  <span>Signup Fees Collected</span>
                  <span className="font-semibold text-red-500">
                    eBa$ {data?.expenses?.signup_fees_collected || 0}
                  </span>
                </li>
                <li className="flex justify-between">
                  <span>Commission Expenses</span>
                  <span className="font-semibold text-red-500">
                    eBa$ {data?.expenses?.commission_expenses || 0}
                  </span>
                </li>
              </ul>
            )}
          </div>
          <div className="rounded-lg bg-white p-6 shadow">
            <h3 className="text-lg font-semibold">Credit Line Metrics</h3>
            {isLoading ? (
              <Skeleton className="mt-4 h-48 w-full" />
            ) : (
              <ul className="mt-4 space-y-2">
                <li className="flex justify-between">
                  <span>Total Credit Line Issued</span>
                  <span className="font-semibold text-red-500">
                    eBa$ {data?.credit_line?.total_credit_line_issued || 0}
                  </span>
                </li>
                <li className="flex justify-between">
                  <span>Credit Line Used</span>
                  <span className="font-semibold text-red-500">
                    eBa$ {data?.credit_line?.credit_line_used || 0}
                  </span>
                </li>
                <li className="flex justify-between">
                  <span>Outstanding Balances</span>
                  <span className="font-semibold text-red-500">
                    eBa$ {data?.credit_line?.outstanding_balances || 0}
                  </span>
                </li>
                <li className="flex justify-between">
                  <span>Overdue Credit Balances</span>
                  <span className="font-semibold text-red-500">
                    eBa$ {data?.credit_line?.overdue_credit_balances || 0}
                  </span>
                </li>
                <li className="flex justify-between">
                  <span>Utilization Rate</span>
                  <span className="font-semibold text-green-500">
                    {data?.credit_line?.utilization_rate || 0}%
                  </span>
                </li>
              </ul>
            )}
          </div>
        </div>

        <div className="mt-8 grid grid-cols-1 gap-6 lg:grid-cols-2">
          <div className="rounded-lg bg-white p-6 shadow">
            <h3 className="text-lg font-semibold">Recent Transactions</h3>
            {isLoading ? (
              <Skeleton className="mt-4 h-48 w-full" />
            ) : (
              <MkdSimpleTable
                columns={recentTransactionsColumns}
                data={recentTransactionsData}
                className="mt-4"
              />
            )}
          </div>
          <div className="rounded-lg bg-white p-6 shadow">
            <h3 className="text-lg font-semibold">Recent Applications</h3>
            {isLoading ? (
              <Skeleton className="mt-4 h-48 w-full" />
            ) : (
              <MkdSimpleTable
                columns={recentApplicationsColumns}
                data={recentApplicationsData}
                className="mt-4"
              />
            )}
          </div>
        </div>
      </div>
    </AdminWrapper>
  );
};

export default AdminDashboardPage;
