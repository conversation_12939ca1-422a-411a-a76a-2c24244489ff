// Fixed duplicate imports
import React, { useState, useEffect } from "react";
import { Modal } from "../Modal";
import { InteractiveButton } from "../InteractiveButton";
import { DownloadIcon, CheckIcon, XIcon } from "../../assets/svgs";
import { PastSubmissionsModal } from "../PastSubmissionsModal";
import { User, Document } from "../../interfaces";
import { useSDK } from "../../hooks/useSDK";
import { useContexts } from "../../hooks/useContexts";

interface VerifyDocumentsModalProps {
  isOpen: boolean;
  onClose: () => void;
  user: User | null;
}

export const VerifyDocumentsModal: React.FC<VerifyDocumentsModalProps> = ({
  isOpen,
  onClose,
  user,
}) => {
  const [isPastSubmissionsOpen, setIsPastSubmissionsOpen] = useState(false);
  const [selectedVerification, setSelectedVerification] = useState("");
  const [documents, setDocuments] = useState<Document[]>([]);
  const { sdk } = useSDK();
  const { globalDispatch } = useContexts();

  useEffect(() => {
    if (isOpen && user) {
      fetchDocuments(user.id);
    }
  }, [isOpen, user]);

  const fetchDocuments = async (userId: number) => {
    try {
      const response = await sdk.callRawAPI(
        `/v2/api/ebadollar/custom/admin/users/${userId}/documents`,
        {},
        "GET"
      );
      setDocuments(response.data);
    } catch (error) {
      console.error("Error fetching documents:", error);
      globalDispatch({
        type: "SNACKBAR",
        payload: { message: "Error fetching documents", toastStatus: "error" },
      });
    }
  };

  const handleUpdateDocumentStatus = async (
    documentId: number,
    status: "approved" | "rejected"
  ) => {
    try {
      await sdk.callRawAPI(
        `/v2/api/ebadollar/custom/admin/documents/${documentId}`,
        { status },
        "PUT"
      );
      globalDispatch({
        type: "SNACKBAR",
        payload: {
          message: `Document ${status}`,
          toastStatus: "success",
        },
      });
      fetchDocuments(user!.id); // Refetch documents to update UI
    } catch (error) {
      console.error("Error updating document status:", error);
      globalDispatch({
        type: "SNACKBAR",
        payload: {
          message: "Error updating document status",
          toastStatus: "error",
        },
      });
    }
  };

  if (!user) return null;

  const handleOpenPastSubmissions = (title: string) => {
    setSelectedVerification(title);
    setIsPastSubmissionsOpen(true);
  };

  const handleClosePastSubmissions = () => {
    setIsPastSubmissionsOpen(false);
    setSelectedVerification("");
  };

  const getDocumentForType = (type: string) => {
    return documents.find((doc) => doc.document_type === type);
  };

  return (
    <Modal
      isOpen={isOpen}
      modalCloseClick={onClose}
      title=""
      modalHeader={false}
      classes={{
        modal: "h-full",
        modalDialog: "w-[90%] max-w-4xl bg-white !p-6",
        modalContent: "!p-0 !m-0",
      }}
    >
      <div className="flex justify-between items-start mb-6">
        <h2 className="text-xl font-bold text-[#1E293B]">Verify documents</h2>
        <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
          <XIcon />
        </button>
      </div>
      <div className="flex items-center mb-8">
        <div className="w-12 h-12 rounded-full bg-gray-300 mr-4">
          {user.photo ? (
            <img
              src={user.photo}
              alt={`${user.first_name} ${user.last_name}`}
              className="w-full h-full rounded-full object-cover"
            />
          ) : (
            <div className="w-full h-full rounded-full bg-gray-300" />
          )}
        </div>
        <div>
          <h3 className="text-lg font-semibold">{`${user.first_name} ${user.last_name}`}</h3>
          <p className="text-sm text-gray-500">Applied on: 20 April 2025</p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <VerificationCard
          title="Government ID Verification"
          icon={<DocumentIcon />}
          document={getDocumentForType("government_id")}
          onApprove={() =>
            handleUpdateDocumentStatus(
              getDocumentForType("government_id")!.id,
              "approved"
            )
          }
          onReject={() =>
            handleUpdateDocumentStatus(
              getDocumentForType("government_id")!.id,
              "rejected"
            )
          }
          onViewPastSubmissions={() =>
            handleOpenPastSubmissions("Government ID Verification")
          }
        />
        <VerificationCard
          title="Selfie Verification"
          icon={<CameraIcon />}
          document={getDocumentForType("selfie")}
          onApprove={() =>
            handleUpdateDocumentStatus(
              getDocumentForType("selfie")!.id,
              "approved"
            )
          }
          onReject={() =>
            handleUpdateDocumentStatus(
              getDocumentForType("selfie")!.id,
              "rejected"
            )
          }
          onViewPastSubmissions={() =>
            handleOpenPastSubmissions("Selfie Verification")
          }
        />
        <VerificationCard
          title="Voice Verification"
          icon={<MicIcon />}
          document={getDocumentForType("voice")}
          onApprove={() =>
            handleUpdateDocumentStatus(
              getDocumentForType("voice")!.id,
              "approved"
            )
          }
          onReject={() =>
            handleUpdateDocumentStatus(
              getDocumentForType("voice")!.id,
              "rejected"
            )
          }
          onViewPastSubmissions={() =>
            handleOpenPastSubmissions("Voice Verification")
          }
        />
      </div>
      <PastSubmissionsModal
        isOpen={isPastSubmissionsOpen}
        onClose={handleClosePastSubmissions}
        user={user}
        title={selectedVerification}
      />
    </Modal>
  );
};

const VerificationCard = ({
  title,
  icon,
  document,
  onApprove,
  onReject,
  onViewPastSubmissions,
}: {
  title: string;
  icon: React.ReactNode;
  document: Document | undefined;
  onApprove: () => void;
  onReject: () => void;
  onViewPastSubmissions: () => void;
}) => (
  <div className="border rounded-lg p-4 flex flex-col items-center bg-white shadow-sm">
    <h4 className="text-md font-semibold mb-4 text-center">{title}</h4>
    <div className="w-full h-40 bg-[#3B4B64] rounded-lg flex items-center justify-center mb-4">
      {document ? (
        <img
          src={document.document_url}
          alt={title}
          className="w-full h-full object-contain"
        />
      ) : (
        icon
      )}
    </div>
    {document ? (
      <>
        <div className="flex gap-2 mb-2 w-full">
          <a
            href={document.document_url}
            download
            target="_blank"
            rel="noopener noreferrer"
            className="flex-1"
          >
            <InteractiveButton className="w-full text-sm bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-lg flex items-center justify-center gap-2">
              <DownloadIcon /> Download
            </InteractiveButton>
          </a>
          <a
            href={document.document_url}
            target="_blank"
            rel="noopener noreferrer"
            className="flex-1"
          >
            <InteractiveButton className="w-full text-sm bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-lg flex items-center justify-center gap-2">
              <EyeIcon /> Preview
            </InteractiveButton>
          </a>
        </div>
        {document.status === "pending" && (
          <>
            <InteractiveButton
              onClick={onApprove}
              className="w-full text-sm bg-[#4CAF50] text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2 mb-2"
            >
              <CheckIcon /> Approve document
            </InteractiveButton>
            <InteractiveButton
              onClick={onReject}
              className="w-full text-sm bg-[#F44336] text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2 mb-2"
            >
              <XIcon /> Reject
            </InteractiveButton>
          </>
        )}
        {document.status === "approved" && (
          <p className="text-green-600 font-bold">Approved</p>
        )}
        {document.status === "rejected" && (
          <p className="text-red-600 font-bold">Rejected</p>
        )}
        <InteractiveButton
          className="w-full text-sm bg-[#3B4B64] text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2 mt-2"
          onClick={onViewPastSubmissions}
        >
          <HistoryIcon /> View past submissions
        </InteractiveButton>
      </>
    ) : (
      <p className="text-gray-500">No document submitted.</p>
    )}
  </div>
);

const DocumentIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    className="h-10 w-10 text-white"
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={1}
      d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
    />
  </svg>
);

const CameraIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    className="h-10 w-10 text-white"
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={1}
      d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"
    />
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={1}
      d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"
    />
  </svg>
);

const MicIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    className="h-10 w-10 text-white"
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={1}
      d="M19 11a7 7 0 01-14 0m7 10v-3a3 3 0 00-3-3H9m6 0h-2.5a3 3 0 00-3 3v3m10-10a7 7 0 00-14 0"
    />
  </svg>
);
const EyeIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    className="h-5 w-5"
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth="2"
      d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
    />
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth="2"
      d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
    />
  </svg>
);

const HistoryIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    className="h-5 w-5"
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth="2"
      d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
    />
  </svg>
);
