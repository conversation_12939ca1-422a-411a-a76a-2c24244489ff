import React, { useState } from "react";
import {
  useStripe,
  useElements,
  CardElement,
  Elements,
} from "@stripe/react-stripe-js";
import { loadStripe } from "@stripe/stripe-js";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import MkdSDK from "../../utils/MkdSDK";
import useCustomerPaymentMethods from "../../hooks/useCustomerPaymentMethods";

// Initialize Stripe
const stripePromise = loadStripe(
  import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY || ""
);

interface BillingDetails {
  name: string;
  address: {
    line1: string;
    city: string;
    state: string;
    postal_code: string;
    country: string;
  };
}

interface StripePaymentFormProps {
  onSuccess: (result: any) => void;
  onCancel: () => void;
  isLoading?: boolean;
  mode?: "add_payment_method" | "process_payment";
  amount?: number;
  currency?: string;
}

const StripePaymentForm: React.FC<StripePaymentFormProps> = ({
  onSuccess,
  onCancel,
  isLoading = false,
  mode = "add_payment_method",
  amount = 0,
  currency = "usd",
}) => {
  const stripe = useStripe();
  const elements = useElements();
  const queryClient = useQueryClient();
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedPaymentMethodId, setSelectedPaymentMethodId] =
    useState<string>("");

  // Get existing payment methods for process_payment mode
  const { paymentMethods, isLoadingPaymentMethods } =
    useCustomerPaymentMethods();

  const [billingDetails, setBillingDetails] = useState<BillingDetails>({
    name: "",
    address: {
      line1: "",
      city: "",
      state: "",
      postal_code: "",
      country: "US",
    },
  });

  const sdk = new MkdSDK();

  // Mutation for attaching PaymentMethod to customer
  const attachPaymentMethod = useMutation({
    mutationFn: async (paymentMethodId: string) => {
      return sdk.attachPaymentMethodToCustomer({
        payment_method_id: paymentMethodId,
      });
    },
    onSuccess: () => {
      // Invalidate payment methods query to refresh the list
      queryClient.invalidateQueries({ queryKey: ["paymentMethods"] });
    },
  });

  // Mutation for processing payment
  const processPayment = useMutation({
    mutationFn: async (paymentMethodId: string) => {
      return sdk.processStripeTopUp({
        amount: amount,
        payment_method_id: paymentMethodId,
        currency: currency,
      });
    },
    onSuccess: () => {
      // Invalidate account balance query to refresh the balance
      queryClient.invalidateQueries({ queryKey: ["accountBalance"] });
    },
  });

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    setProcessing(true);
    setError(null);

    try {
      let paymentMethodId: string;

      if (mode === "process_payment" && selectedPaymentMethodId) {
        // Use existing payment method
        paymentMethodId = selectedPaymentMethodId;
      } else {
        // Create new payment method using Stripe Elements
        if (!stripe || !elements) {
          setError("Stripe has not loaded yet. Please try again.");
          return;
        }

        const cardElement = elements.getElement(CardElement);
        if (!cardElement) {
          setError("Card element not found. Please refresh and try again.");
          return;
        }

        const { error: stripeError, paymentMethod } =
          await stripe.createPaymentMethod({
            type: "card",
            card: cardElement,
            billing_details: {
              name: billingDetails.name,
              address: {
                line1: billingDetails.address.line1,
                city: billingDetails.address.city,
                state: billingDetails.address.state,
                postal_code: billingDetails.address.postal_code,
                country: billingDetails.address.country,
              },
            },
          });

        if (stripeError) {
          setError(stripeError.message || "Failed to create payment method");
          return;
        }

        if (!paymentMethod) {
          setError("Failed to create payment method");
          return;
        }

        paymentMethodId = paymentMethod.id;
      }

      if (mode === "add_payment_method") {
        // Attach PaymentMethod to customer
        const attachResult =
          await attachPaymentMethod.mutateAsync(paymentMethodId);

        if (attachResult.error) {
          setError(attachResult.message || "Failed to attach payment method");
          return;
        }

        // Success - call the onSuccess callback
        onSuccess({ id: paymentMethodId });
      } else if (mode === "process_payment") {
        // Process payment directly
        const paymentResult = await processPayment.mutateAsync(paymentMethodId);

        if (paymentResult.error) {
          setError(paymentResult.message || "Failed to process payment");
          return;
        }

        // Success - call the onSuccess callback with payment result
        onSuccess(paymentResult.data);
      }
    } catch (err: any) {
      console.error("Payment processing error:", err);
      setError(
        err.message ||
          `Failed to ${mode === "add_payment_method" ? "add payment method" : "process payment"}`
      );
    } finally {
      setProcessing(false);
    }
  };

  const cardElementOptions = {
    style: {
      base: {
        fontSize: "16px",
        color: "#424770",
        "::placeholder": {
          color: "#aab7c4",
        },
      },
      invalid: {
        color: "#9e2146",
      },
    },
  };

  const formatCardDisplay = (paymentMethod: any) => {
    if (paymentMethod.card) {
      const brand =
        paymentMethod.card.brand.charAt(0).toUpperCase() +
        paymentMethod.card.brand.slice(1);
      return `${brand} •••• ${paymentMethod.card.last4}`;
    }
    return "Card";
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {mode === "process_payment" ? (
        // Show existing payment methods for payment processing
        <>
          {/* Amount Display */}
          <div className="mb-6">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="text-center">
                <p className="text-sm text-gray-600 mb-1">Top-up Amount</p>
                <p className="text-2xl font-bold text-[#0F2C59]">
                  {amount} EBA$
                </p>
                <p className="text-sm text-gray-500">
                  ≈ ${(amount * 1.45).toFixed(2)} USD
                </p>
              </div>
            </div>
          </div>

          {/* Payment Method Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Select Payment Method
            </label>

            {isLoadingPaymentMethods ? (
              <div className="text-center py-4">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#0F2C59] mx-auto"></div>
                <p className="text-sm text-gray-500 mt-2">
                  Loading payment methods...
                </p>
              </div>
            ) : paymentMethods.length === 0 ? (
              <div className="text-center py-4">
                <p className="text-sm text-gray-500 mb-2">
                  No payment methods found
                </p>
                <p className="text-xs text-gray-400">
                  Please add a payment method first
                </p>
              </div>
            ) : (
              <div className="space-y-2">
                {paymentMethods.map((method) => (
                  <label
                    key={method.id}
                    className="flex items-center p-3 border rounded-lg hover:bg-gray-50 cursor-pointer"
                  >
                    <input
                      type="radio"
                      name="paymentMethod"
                      value={method.id}
                      checked={selectedPaymentMethodId === method.id}
                      onChange={(e) =>
                        setSelectedPaymentMethodId(e.target.value)
                      }
                      className="mr-3 text-[#0F2C59] focus:ring-[#0F2C59]"
                    />
                    <div className="flex-1">
                      <div className="flex items-center">
                        <span className="text-blue-600 mr-2">💳</span>
                        <span className="text-sm font-medium">
                          {formatCardDisplay(method)}
                        </span>
                      </div>
                      {method.card && (
                        <p className="text-xs text-gray-500 ml-6">
                          Expires{" "}
                          {method.card.exp_month.toString().padStart(2, "0")}/
                          {method.card.exp_year}
                        </p>
                      )}
                    </div>
                  </label>
                ))}
              </div>
            )}
          </div>
        </>
      ) : (
        // Show card input form for adding new payment method
        <>
          {/* Cardholder Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Cardholder Name
            </label>
            <input
              type="text"
              value={billingDetails.name}
              onChange={(e) =>
                setBillingDetails((prev) => ({ ...prev, name: e.target.value }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="John Doe"
              required
            />
          </div>

          {/* Card Element */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Card Information
            </label>
            <div className="p-3 border border-gray-300 rounded-md">
              <CardElement options={cardElementOptions} />
            </div>
          </div>
        </>
      )}

      {/* Billing Address - Only show for add_payment_method mode */}
      {mode === "add_payment_method" && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Address Line 1
            </label>
            <input
              type="text"
              value={billingDetails.address.line1}
              onChange={(e) =>
                setBillingDetails((prev) => ({
                  ...prev,
                  address: { ...prev.address, line1: e.target.value },
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="123 Main St"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              City
            </label>
            <input
              type="text"
              value={billingDetails.address.city}
              onChange={(e) =>
                setBillingDetails((prev) => ({
                  ...prev,
                  address: { ...prev.address, city: e.target.value },
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="New York"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              State
            </label>
            <input
              type="text"
              value={billingDetails.address.state}
              onChange={(e) =>
                setBillingDetails((prev) => ({
                  ...prev,
                  address: { ...prev.address, state: e.target.value },
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="NY"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              ZIP Code
            </label>
            <input
              type="text"
              value={billingDetails.address.postal_code}
              onChange={(e) =>
                setBillingDetails((prev) => ({
                  ...prev,
                  address: { ...prev.address, postal_code: e.target.value },
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="10001"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Country
            </label>
            <select
              value={billingDetails.address.country}
              onChange={(e) =>
                setBillingDetails((prev) => ({
                  ...prev,
                  address: { ...prev.address, country: e.target.value },
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="US">United States</option>
              <option value="CA">Canada</option>
              <option value="GB">United Kingdom</option>
              {/* Add more countries as needed */}
            </select>
          </div>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="text-red-600 text-sm bg-red-50 p-3 rounded-md">
          {error}
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex justify-end space-x-3 pt-4">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500"
          disabled={processing || isLoading}
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={
            processing ||
            isLoading ||
            (mode === "add_payment_method" && !stripe) ||
            (mode === "process_payment" && !selectedPaymentMethodId)
          }
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {processing || isLoading
            ? "Processing..."
            : mode === "process_payment"
              ? `Pay $${(amount * 1.45).toFixed(2)}`
              : "Add Payment Method"}
        </button>
      </div>
    </form>
  );
};

interface StripePaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (result: any) => void;
  mode?: "add_payment_method" | "process_payment";
  amount?: number;
  currency?: string;
}

const StripePaymentModal: React.FC<StripePaymentModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  mode = "add_payment_method",
  amount = 0,
  currency = "usd",
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
        <h2 className="text-xl font-semibold mb-4">
          {mode === "process_payment"
            ? "Complete Payment"
            : "Add Payment Method"}
        </h2>
        <Elements stripe={stripePromise}>
          <StripePaymentForm
            onSuccess={onSuccess}
            onCancel={onClose}
            mode={mode}
            amount={amount}
            currency={currency}
          />
        </Elements>
      </div>
    </div>
  );
};

export default StripePaymentModal;
