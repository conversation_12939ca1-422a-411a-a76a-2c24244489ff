import React, { useState, useEffect, useCallback } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { MemberWrapper } from "../../../components/MemberWrapper";
import InteractiveButton from "@/components/InteractiveButton/InteractiveButton";
import { MkdInputV2 } from "@/components/MkdInputV2";
import { useSDK } from "@/hooks/useSDK";
import { useToast } from "@/hooks/useToast";
import { MkdLoader } from "@/components/MkdLoader";
import TopUpRequestModal from "@/components/TopUpRequestModal";
import StripePaymentModal from "@/components/StripePaymentModal/StripePaymentModal";
import useCustomerPaymentMethods from "@/hooks/useCustomerPaymentMethods";
import CreateNewListingModal from "@/components/CreateNewListingModal";
import ViewListingModal from "@/components/ViewListingModal";

interface IUserProfile {
  email: string;
  first_name: string;
  last_name: string;
  phone?: string | null;
  photo?: string | null;
  address?: string | null;
  city?: string | null;
  state?: string | null;
  zip_code?: string | null;
  country?: string | null;
}

interface TopUpMethod {
  id: string;
  name: string;
  icon: string;
}

interface PendingRequest {
  id: string;
  amount: number;
  method: string;
  date?: string;
  created_at: string;
}

interface TopUpHistory {
  date: string;
  method: string;
  reference: string;
  amount: string;
  status: "Pending" | "Approved";
}

interface ExchangeListing {
  id: string;
  type: "selling" | "buying";
  amount: number;
  rate: number;
  total_value: number;
  status: string;
  user_id: number;
  created_at: string;
}

// Form schemas
const personalDetailsSchema = yup.object({
  first_name: yup.string().required("First name is required"),
  last_name: yup.string().required("Last name is required"),
  email: yup.string().email("Invalid email").required("Email is required"),
  phone: yup.string().nullable(),
  address: yup.string().nullable(),
  city: yup.string().nullable(),
  state: yup.string().nullable(),
  zip_code: yup.string().nullable(),
  country: yup.string().nullable(),
});

const passwordSchema = yup.object({
  current_password: yup.string().required("Current password is required"),
  new_password: yup
    .string()
    .min(8, "Password must be at least 8 characters")
    .matches(
      /^(?=.*[A-Za-z])(?=.*\d)/,
      "Password must contain at least one letter and one number"
    )
    .required("New password is required"),
  confirm_password: yup
    .string()
    .oneOf([yup.ref("new_password")], "Passwords must match")
    .required("Please confirm your password"),
});

const MemberAccountPage = () => {
  const { sdk } = useSDK();
  const { success, error: showError } = useToast();
  const {
    paymentMethods: stripePaymentMethods,
    legacyPaymentMethods,
    detachPaymentMethod,
    setDefaultPaymentMethod,
    removeLegacyPaymentMethod,
    formatCardBrand,
    isDetaching,
    isSettingDefault,
    isRemoving,
  } = useCustomerPaymentMethods();

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [profile, setProfile] = useState<IUserProfile | null>(null);
  const [saving, setSaving] = useState(false);
  const [editingPersonalDetails, setEditingPersonalDetails] = useState(false);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState(
    "Credit Card (Visa ending in 4242)"
  );
  const [topUpAmount, setTopUpAmount] = useState("");
  const [showTopUpModal, setShowTopUpModal] = useState(false);
  const [showAddCardModal, setShowAddCardModal] = useState(false);
  const [showCreateListingModal, setShowCreateListingModal] = useState(false);
  const [showViewListingModal, setShowViewListingModal] = useState(false);
  const [selectedListing, setSelectedListing] = useState<any>(null);
  const [activeExchangeTab, setActiveExchangeTab] = useState<"buy" | "sell">(
    "buy"
  );

  const [accountBalance, setAccountBalance] = useState<any>(null);
  const [topUpHistory, setTopUpHistory] = useState<TopUpHistory[]>([]);
  const [pendingRequests, setPendingRequests] = useState<PendingRequest[]>([]);
  const [exchangeListings, setExchangeListings] = useState<ExchangeListing[]>(
    []
  );
  const [buyRequests, setBuyRequests] = useState<any[]>([]);

  // Form hooks
  const {
    register: registerPersonalDetails,
    handleSubmit: handlePersonalDetailsSubmit,
    formState: { errors: personalDetailsErrors },
    setValue: setPersonalDetailsValue,
    reset: resetPersonalDetails,
  } = useForm({
    resolver: yupResolver(personalDetailsSchema),
  });

  const {
    register: registerPassword,
    handleSubmit: handlePasswordSubmit,
    formState: { errors: passwordErrors },
    reset: resetPassword,
  } = useForm({
    resolver: yupResolver(passwordSchema),
  });

  const topUpMethods: TopUpMethod[] = [
    { id: "paypal", name: "PayPal", icon: "💳" },
    { id: "bank", name: "Bank Transfer", icon: "🏦" },
    { id: "credit", name: "Credit/Debit Card", icon: "💳" },
    { id: "etransfer", name: "e-Transfer", icon: "📱" },
    { id: "wire", name: "Local Wire Transfer (Jamaica)", icon: "🏛️" },
  ];

  const fetchAccountData = useCallback(async () => {
    try {
      setError(null);

      // Payment methods are now handled by useCustomerPaymentMethods hook

      // Fetch account balance
      const balanceResponse = await sdk.request({
        endpoint: "/v2/api/ebadollar/custom/member/account-balance",
        method: "GET",
      });

      console.log("Account Balance API Response:", balanceResponse);

      if (!balanceResponse.error) {
        setAccountBalance({
          balance: Number(balanceResponse.data?.balance || 0),
          last_topped_up: balanceResponse.data?.last_topped_up || null,
        });
      }

      // Fetch top-up requests
      const requestsResponse = await sdk.request({
        endpoint: "/v2/api/ebadollar/custom/member/top-up-requests",
        method: "GET",
        params: { status: "pending" },
      });

      console.log("Top-up Requests API Response:", requestsResponse);

      if (!requestsResponse.error) {
        const processedRequests = Array.isArray(requestsResponse.data)
          ? requestsResponse.data.map((request: any) => ({
              id: String(request.id || ""),
              amount: Number(request.amount || 0),
              method: String(request.method || ""),
              date: request.date || undefined,
              created_at: String(request.created_at || ""),
            }))
          : [];
        setPendingRequests(processedRequests);
      }

      // Fetch top-up history
      const historyResponse = await sdk.request({
        endpoint: "/v2/api/ebadollar/custom/member/top-up-history",
        method: "GET",
      });

      console.log("Top-up History API Response:", historyResponse);

      if (!historyResponse.error) {
        const processedHistory = Array.isArray(historyResponse.data)
          ? historyResponse.data.map((item: any) => ({
              date: String(item.date || ""),
              method: String(item.method || ""),
              reference: String(item.reference || ""),
              amount: String(item.amount || ""),
              status: String(item.status || "Pending") as
                | "Pending"
                | "Approved",
            }))
          : [];
        setTopUpHistory(processedHistory);
      }

      // Fetch exchange listings
      const listingsResponse = await sdk.request({
        endpoint: "/v2/api/ebadollar/custom/member/exchange-listings",
        method: "GET",
        params: {
          type: activeExchangeTab === "buy" ? "selling" : "",
          include_own: "true",
        },
      });

      console.log("Exchange Listings API Response:", listingsResponse);

      if (!listingsResponse.error) {
        const processedListings = Array.isArray(listingsResponse.data)
          ? listingsResponse.data.map((listing: any) => ({
              id: String(listing.id || ""),
              type: String(listing.type || "selling") as "selling" | "buying",
              amount: Number(listing.amount || 0),
              rate: Number(listing.rate || 0),
              total_value: Number(listing.total_value || 0),
              status: String(listing.status || ""),
              user_id: Number(listing.user_id || 0),
              created_at: String(listing.created_at || ""),
            }))
          : [];
        setExchangeListings(processedListings);
      }

      // Fetch buy requests (as seller)
      const buyRequestsResponse = await sdk.request({
        endpoint: "/v2/api/ebadollar/custom/member/buy-requests/received",
        method: "GET",
      });

      console.log("Buy Requests API Response:", buyRequestsResponse);

      if (!buyRequestsResponse.error) {
        setBuyRequests(
          Array.isArray(buyRequestsResponse.data)
            ? buyRequestsResponse.data
            : []
        );
      }
    } catch (error: any) {
      console.error("Error fetching account data:", error);
      setError(String(error?.message || "Failed to load account data"));
    }
  }, []);

  const handleViewListing = (listing: any) => {
    setSelectedListing({
      id: listing.id,
      sellerName: "EBA_Trader01",
      amount: `${listing.amount} EBA$`,
      rate: `$${listing.rate} per EBA$`,
      totalAmount: `$${listing.total_value}`,
      paymentMethod: "Bank Transfer",
    });
    setShowViewListingModal(true);
  };

  const handleApproveBuyRequest = useCallback(async (requestId: string) => {
    try {
      const response = await sdk.request({
        endpoint: `/v2/api/ebadollar/custom/member/buy-requests/${requestId}/status`,
        method: "PUT",
        body: { status: "approved" },
      });

      console.log("Approve Buy Request API Response:", response);

      if (!response.error) {
        success("Buy request approved successfully");
        fetchAccountData(); // Refresh data
      } else {
        showError(String(response.message || "Failed to approve buy request"));
      }
    } catch (error: any) {
      console.error("Error approving buy request:", error);
      showError(String(error?.message || "Failed to approve buy request"));
    }
  }, []);

  // Form submission handlers
  const onPersonalDetailsSubmit = useCallback(
    async (data: any) => {
      try {
        setSaving(true);

        const response = await sdk.request({
          endpoint: "/v2/api/ebadollar/custom/member/profile",
          method: "PUT",
          body: {
            first_name: data.first_name,
            last_name: data.last_name,
            phone: data.phone,
            address: data.address,
            city: data.city,
            state: data.state,
            zip_code: data.zip_code,
            country: data.country,
          },
        });

        console.log("Update Profile API Response:", response);

        if (!response.error) {
          success("Profile updated successfully!");
          setEditingPersonalDetails(false);
          fetchProfile(); // Refresh profile data
        } else {
          showError(String(response.message || "Failed to update profile"));
        }
      } catch (error: any) {
        console.error("Error updating profile:", error);
        showError(String(error?.message || "Failed to update profile"));
      } finally {
        setSaving(false);
      }
    },
    [sdk, success, showError]
  );

  const onPasswordSubmit = useCallback(
    async (data: any) => {
      try {
        setSaving(true);

        const response = await sdk.request({
          endpoint: "/v2/api/ebadollar/custom/member/change-password",
          method: "POST",
          body: {
            current_password: data.current_password,
            new_password: data.new_password,
          },
        });

        console.log("Change Password API Response:", response);

        if (!response.error) {
          success("Password updated successfully!");
          resetPassword();
        } else {
          showError(String(response.message || "Failed to update password"));
        }
      } catch (error: any) {
        console.error("Error updating password:", error);
        showError(String(error?.message || "Failed to update password"));
      } finally {
        setSaving(false);
      }
    },
    [sdk, success, showError, resetPassword]
  );

  // Top-up request handlers
  const handleTopUpRequest = useCallback(
    async (topUpData: any) => {
      try {
        setSaving(true);

        const response = await sdk.request({
          endpoint: "/v2/api/ebadollar/custom/member/top-up-requests",
          method: "POST",
          body: {
            amount: topUpData.amount,
            method: topUpData.method,
            payment_method_id: topUpData.payment_method_id,
            reference: topUpData.reference,
            notes: topUpData.notes,
          },
        });

        console.log("Top-up Request API Response:", response);

        if (!response.error) {
          success(
            "Top-up request submitted successfully! We'll process it within 24 hours."
          );
          setShowTopUpModal(false);
          fetchAccountData(); // Refresh account data
        } else {
          showError(
            String(response.message || "Failed to submit top-up request")
          );
        }
      } catch (error: any) {
        console.error("Error submitting top-up request:", error);
        showError(String(error?.message || "Failed to submit top-up request"));
      } finally {
        setSaving(false);
      }
    },
    [sdk, success, showError, fetchAccountData]
  );

  const handleCancelTopUpRequest = useCallback(
    async (requestId: string) => {
      if (!confirm("Are you sure you want to cancel this top-up request?")) {
        return;
      }

      try {
        setSaving(true);

        const response = await sdk.request({
          endpoint: `/v2/api/ebadollar/custom/member/top-up-requests/${requestId}/cancel`,
          method: "PUT",
        });

        console.log("Cancel Top-up Request API Response:", response);

        if (!response.error) {
          success("Top-up request cancelled successfully!");
          fetchAccountData(); // Refresh account data
        } else {
          showError(
            String(response.message || "Failed to cancel top-up request")
          );
        }
      } catch (error: any) {
        console.error("Error cancelling top-up request:", error);
        showError(String(error?.message || "Failed to cancel top-up request"));
      } finally {
        setSaving(false);
      }
    },
    [sdk, success, showError, fetchAccountData]
  );

  // EBA Dollar Exchange handlers
  const handleDirectPurchase = useCallback(async () => {
    if (!topUpAmount || !selectedPaymentMethod) {
      showError("Please enter amount and select payment method");
      return;
    }

    try {
      setSaving(true);

      const response = await sdk.request({
        endpoint: "/v2/api/ebadollar/custom/member/eba-purchase",
        method: "POST",
        body: {
          amount: parseFloat(topUpAmount),
          payment_method: selectedPaymentMethod,
        },
      });

      console.log("Direct Purchase API Response:", response);

      if (!response.error) {
        success(
          `Successfully purchased ${topUpAmount} EBA$ for $${(parseFloat(topUpAmount) * 1.45).toFixed(2)}`
        );
        setTopUpAmount("");
        fetchAccountData(); // Refresh account data
      } else {
        showError(String(response.message || "Failed to purchase EBA dollars"));
      }
    } catch (error: any) {
      console.error("Error purchasing EBA dollars:", error);
      showError(String(error?.message || "Failed to purchase EBA dollars"));
    } finally {
      setSaving(false);
    }
  }, [
    topUpAmount,
    selectedPaymentMethod,
    sdk,
    success,
    showError,
    fetchAccountData,
  ]);

  // P2P Exchange handlers
  const handleCreateListing = useCallback(
    async (listingData: any) => {
      try {
        setSaving(true);

        const response = await sdk.request({
          endpoint: "/v2/api/ebadollar/custom/member/exchange-listings",
          method: "POST",
          body: {
            type: listingData.type, // "buying" or "selling"
            amount: parseFloat(listingData.amount),
            rate: parseFloat(listingData.rate),
            payment_method: listingData.payment_method,
            description: listingData.description,
          },
        });

        console.log("Create Listing API Response:", response);

        if (!response.error) {
          success("Listing created successfully!");
          setShowCreateListingModal(false);
          fetchAccountData(); // Refresh listings
        } else {
          showError(String(response.message || "Failed to create listing"));
        }
      } catch (error: any) {
        console.error("Error creating listing:", error);
        showError(String(error?.message || "Failed to create listing"));
      } finally {
        setSaving(false);
      }
    },
    [sdk, success, showError, fetchAccountData]
  );

  const handleDeleteListing = useCallback(
    async (listingId: string) => {
      if (!confirm("Are you sure you want to delete this listing?")) {
        return;
      }

      try {
        setSaving(true);

        const response = await sdk.request({
          endpoint: `/v2/api/ebadollar/custom/member/exchange-listings/${listingId}`,
          method: "DELETE",
        });

        console.log("Delete Listing API Response:", response);

        if (!response.error) {
          success("Listing deleted successfully!");
          fetchAccountData(); // Refresh listings
        } else {
          showError(String(response.message || "Failed to delete listing"));
        }
      } catch (error: any) {
        console.error("Error deleting listing:", error);
        showError(String(error?.message || "Failed to delete listing"));
      } finally {
        setSaving(false);
      }
    },
    [sdk, success, showError, fetchAccountData]
  );

  const handleBuyRequest = useCallback(
    async (listingId: string, amount: number) => {
      try {
        setSaving(true);

        const response = await sdk.request({
          endpoint: "/v2/api/ebadollar/custom/member/buy-requests",
          method: "POST",
          body: {
            listing_id: listingId,
            amount: amount,
          },
        });

        console.log("Buy Request API Response:", response);

        if (!response.error) {
          success(
            "Buy request sent successfully! The seller will be notified."
          );
          fetchAccountData(); // Refresh data
        } else {
          showError(String(response.message || "Failed to send buy request"));
        }
      } catch (error: any) {
        console.error("Error sending buy request:", error);
        showError(String(error?.message || "Failed to send buy request"));
      } finally {
        setSaving(false);
      }
    },
    [sdk, success, showError, fetchAccountData]
  );

  const fetchProfile = useCallback(async () => {
    setLoading(true);
    try {
      setError(null);

      const result = await sdk.request({
        endpoint: "/v2/api/ebadollar/custom/member/profile",
        method: "GET",
      });

      console.log("Profile API Response:", result);

      if (!result.error) {
        const data = result.data;
        const profileData = data?.profile || data;

        const processedProfile = {
          email: String(data?.email || ""),
          first_name: String(profileData.first_name || ""),
          last_name: String(profileData.last_name || ""),
          phone: profileData.phone || null,
          photo: profileData.profile_photo || profileData.photo || null,
          address: profileData.address || null,
          city: profileData.city || null,
          state: profileData.state || null,
          zip_code: profileData.zip_code || null,
          country: profileData.country || null,
        };

        setProfile(processedProfile);

        // Populate form with current data
        setPersonalDetailsValue("first_name", processedProfile.first_name);
        setPersonalDetailsValue("last_name", processedProfile.last_name);
        setPersonalDetailsValue("email", processedProfile.email);
        setPersonalDetailsValue("phone", processedProfile.phone || "");
        setPersonalDetailsValue("address", processedProfile.address || "");
        setPersonalDetailsValue("city", processedProfile.city || "");
        setPersonalDetailsValue("state", processedProfile.state || "");
        setPersonalDetailsValue("zip_code", processedProfile.zip_code || "");
        setPersonalDetailsValue("country", processedProfile.country || "");
      } else {
        console.error("Error fetching profile:", result.message);
        setError(String(result.message || "Failed to load profile"));
      }
    } catch (error: any) {
      console.error("Error fetching profile:", error);
      setError(String(error?.message || "Failed to load profile"));
    } finally {
      setLoading(false);
    }
  }, [sdk, setPersonalDetailsValue]);

  useEffect(() => {
    fetchProfile();
    fetchAccountData();
  }, []);

  if (loading) {
    return (
      <MemberWrapper>
        <div className="flex justify-center items-center py-12">
          <MkdLoader />
        </div>
      </MemberWrapper>
    );
  }

  if (error && !profile) {
    return (
      <MemberWrapper>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="text-4xl mb-4">⚠️</div>
            <p className="text-white mb-4">{error}</p>
            <button
              onClick={() => {
                fetchProfile();
                fetchAccountData();
              }}
              className="px-4 py-2 bg-[#E63946] text-white rounded hover:bg-opacity-90"
            >
              Retry
            </button>
          </div>
        </div>
      </MemberWrapper>
    );
  }

  return (
    <MemberWrapper>
      <div className="p-8 bg-[#0F2C59] min-h-screen">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-white mb-2">My Account</h1>
        </div>

        <div className="space-y-6">
          {/* Profile Header */}
          <div className="bg-white rounded-lg p-6">
            <div className="flex items-center space-x-4">
              <div className="w-16 h-16 rounded-full overflow-hidden">
                <img
                  src={
                    profile?.photo ||
                    "https://images.unsplash.com/photo-*************-5658abf4ff4e?w=150&h=150&fit=crop&crop=face"
                  }
                  alt="Profile"
                  className="w-full h-full object-cover"
                />
              </div>
              <div>
                <h2 className="text-2xl font-semibold text-gray-900">
                  {profile?.first_name} {profile?.last_name}
                </h2>
                <p className="text-gray-600">{profile?.email}</p>
                <p className="text-sm text-gray-500">
                  Member since: April 2023
                </p>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Personal Details */}
            <div className="bg-white rounded-lg p-6">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-xl font-semibold text-gray-900">
                  Personal Details
                </h3>
                <InteractiveButton
                  onClick={() => {
                    if (editingPersonalDetails) {
                      setEditingPersonalDetails(false);
                      resetPersonalDetails();
                    } else {
                      setEditingPersonalDetails(true);
                    }
                  }}
                  className="text-[#E63946] hover:underline text-sm font-medium bg-transparent border-none"
                >
                  {editingPersonalDetails ? "Cancel" : "Edit"}
                </InteractiveButton>
              </div>

              {editingPersonalDetails ? (
                <form
                  onSubmit={handlePersonalDetailsSubmit(
                    onPersonalDetailsSubmit
                  )}
                  className="space-y-4"
                >
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <MkdInputV2
                        {...registerPersonalDetails("first_name")}
                        errors={personalDetailsErrors.first_name?.message}
                      >
                        <MkdInputV2.Container>
                          <MkdInputV2.Label>First Name*</MkdInputV2.Label>
                          <MkdInputV2.Field
                            placeholder="Enter first name"
                            className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                          />
                          <MkdInputV2.Error />
                        </MkdInputV2.Container>
                      </MkdInputV2>
                    </div>

                    <div>
                      <MkdInputV2
                        {...registerPersonalDetails("last_name")}
                        errors={personalDetailsErrors.last_name?.message}
                      >
                        <MkdInputV2.Container>
                          <MkdInputV2.Label>Last Name*</MkdInputV2.Label>
                          <MkdInputV2.Field
                            placeholder="Enter last name"
                            className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                          />
                          <MkdInputV2.Error />
                        </MkdInputV2.Container>
                      </MkdInputV2>
                    </div>
                  </div>

                  <div>
                    <MkdInputV2
                      {...registerPersonalDetails("email")}
                      errors={personalDetailsErrors.email?.message}
                    >
                      <MkdInputV2.Container>
                        <MkdInputV2.Label>Email Address*</MkdInputV2.Label>
                        <MkdInputV2.Field
                          placeholder="Enter email address"
                          className="!border-[#D1D5DB] !bg-gray-100 !text-gray-600"
                        />
                        <MkdInputV2.Error />
                      </MkdInputV2.Container>
                    </MkdInputV2>
                    <p className="text-xs text-gray-500 mt-1">
                      Email cannot be changed. Contact support if needed.
                    </p>
                  </div>

                  <div>
                    <MkdInputV2
                      {...registerPersonalDetails("phone")}
                      errors={personalDetailsErrors.phone?.message}
                    >
                      <MkdInputV2.Container>
                        <MkdInputV2.Label>Phone Number</MkdInputV2.Label>
                        <MkdInputV2.Field
                          placeholder="Enter phone number"
                          className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                        />
                        <MkdInputV2.Error />
                      </MkdInputV2.Container>
                    </MkdInputV2>
                  </div>

                  <div>
                    <MkdInputV2
                      {...registerPersonalDetails("address")}
                      errors={personalDetailsErrors.address?.message}
                    >
                      <MkdInputV2.Container>
                        <MkdInputV2.Label>Address</MkdInputV2.Label>
                        <MkdInputV2.Field
                          placeholder="Enter address"
                          className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                        />
                        <MkdInputV2.Error />
                      </MkdInputV2.Container>
                    </MkdInputV2>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <MkdInputV2
                        {...registerPersonalDetails("city")}
                        errors={personalDetailsErrors.city?.message}
                      >
                        <MkdInputV2.Container>
                          <MkdInputV2.Label>City</MkdInputV2.Label>
                          <MkdInputV2.Field
                            placeholder="Enter city"
                            className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                          />
                          <MkdInputV2.Error />
                        </MkdInputV2.Container>
                      </MkdInputV2>
                    </div>

                    <div>
                      <MkdInputV2
                        {...registerPersonalDetails("state")}
                        errors={personalDetailsErrors.state?.message}
                      >
                        <MkdInputV2.Container>
                          <MkdInputV2.Label>State/Province</MkdInputV2.Label>
                          <MkdInputV2.Field
                            placeholder="Enter state"
                            className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                          />
                          <MkdInputV2.Error />
                        </MkdInputV2.Container>
                      </MkdInputV2>
                    </div>

                    <div>
                      <MkdInputV2
                        {...registerPersonalDetails("zip_code")}
                        errors={personalDetailsErrors.zip_code?.message}
                      >
                        <MkdInputV2.Container>
                          <MkdInputV2.Label>ZIP Code</MkdInputV2.Label>
                          <MkdInputV2.Field
                            placeholder="Enter ZIP code"
                            className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                          />
                          <MkdInputV2.Error />
                        </MkdInputV2.Container>
                      </MkdInputV2>
                    </div>
                  </div>

                  <div>
                    <MkdInputV2
                      {...registerPersonalDetails("country")}
                      errors={personalDetailsErrors.country?.message}
                    >
                      <MkdInputV2.Container>
                        <MkdInputV2.Label>Country</MkdInputV2.Label>
                        <MkdInputV2.Field
                          placeholder="Enter country"
                          className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                        />
                        <MkdInputV2.Error />
                      </MkdInputV2.Container>
                    </MkdInputV2>
                  </div>

                  <div className="flex justify-end gap-3 pt-4">
                    <InteractiveButton
                      type="button"
                      onClick={() => {
                        setEditingPersonalDetails(false);
                        resetPersonalDetails();
                      }}
                      className="px-4 py-2 border border-gray-300 text-gray-700 bg-white hover:bg-gray-50 rounded-md"
                    >
                      Cancel
                    </InteractiveButton>
                    <InteractiveButton
                      type="submit"
                      loading={saving}
                      disabled={saving}
                      className="px-6 py-2 bg-[#0F2C59] hover:bg-[#0F2C59]/90 text-white rounded-md"
                    >
                      Save Changes
                    </InteractiveButton>
                  </div>
                </form>
              ) : (
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Full Name
                    </label>
                    <p className="text-gray-900">
                      {profile?.first_name} {profile?.last_name}
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Email Address
                    </label>
                    <p className="text-gray-900">{profile?.email}</p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Phone Number
                    </label>
                    <p className="text-gray-900">
                      {profile?.phone || "Not provided"}
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Address
                    </label>
                    <p className="text-gray-900">
                      {profile?.address || "Not provided"}
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        City
                      </label>
                      <p className="text-gray-900">
                        {profile?.city || "Not provided"}
                      </p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        State/Province
                      </label>
                      <p className="text-gray-900">
                        {profile?.state || "Not provided"}
                      </p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        ZIP Code
                      </label>
                      <p className="text-gray-900">
                        {profile?.zip_code || "Not provided"}
                      </p>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Country
                    </label>
                    <p className="text-gray-900">
                      {profile?.country || "Not provided"}
                    </p>
                  </div>
                </div>
              )}
            </div>

            {/* Password Settings */}
            <div className="bg-white rounded-lg p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-6">
                Password Settings
              </h3>

              <p className="text-gray-600 text-sm mb-6">
                Password must be at least 8 characters and contain at least one
                letter and one number.
              </p>

              <form
                onSubmit={handlePasswordSubmit(onPasswordSubmit)}
                className="space-y-4"
              >
                <div>
                  <MkdInputV2
                    {...registerPassword("current_password")}
                    errors={passwordErrors.current_password?.message}
                  >
                    <MkdInputV2.Container>
                      <MkdInputV2.Label>Current Password*</MkdInputV2.Label>
                      <MkdInputV2.Field
                        placeholder="Enter current password"
                        className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                      />
                      <MkdInputV2.Error />
                    </MkdInputV2.Container>
                  </MkdInputV2>
                </div>

                <div>
                  <MkdInputV2
                    {...registerPassword("new_password")}
                    errors={passwordErrors.new_password?.message}
                  >
                    <MkdInputV2.Container>
                      <MkdInputV2.Label>New Password*</MkdInputV2.Label>
                      <MkdInputV2.Field
                        placeholder="Enter new password"
                        className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                      />
                      <MkdInputV2.Error />
                    </MkdInputV2.Container>
                  </MkdInputV2>
                </div>

                <div>
                  <MkdInputV2
                    {...registerPassword("confirm_password")}
                    errors={passwordErrors.confirm_password?.message}
                  >
                    <MkdInputV2.Container>
                      <MkdInputV2.Label>Confirm New Password*</MkdInputV2.Label>
                      <MkdInputV2.Field
                        placeholder="Confirm new password"
                        className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                      />
                      <MkdInputV2.Error />
                    </MkdInputV2.Container>
                  </MkdInputV2>
                </div>

                <InteractiveButton
                  type="submit"
                  loading={saving}
                  disabled={saving}
                  className="bg-[#0F2C59] hover:bg-[#0F2C59]/90 text-white px-6 py-2 rounded-md"
                >
                  Update Password
                </InteractiveButton>
              </form>
            </div>
          </div>

          {/* Payment Methods */}
          <div className="bg-white rounded-lg p-6">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-semibold text-gray-900">
                Payment Methods
              </h3>
              <InteractiveButton
                onClick={() => setShowAddCardModal(true)}
                className="bg-[#0F2C59] hover:bg-[#0F2C59]/90 text-white px-4 py-2 rounded-md text-sm"
              >
                + Add New Card
              </InteractiveButton>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Stripe PaymentMethods */}
              {stripePaymentMethods.map((method) => (
                <div
                  key={method.id}
                  className="border border-gray-200 rounded-lg p-4 relative"
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-6 bg-blue-600 rounded flex items-center justify-center">
                        <span className="text-white text-xs font-bold">
                          {method.card?.brand?.toUpperCase() || "CARD"}
                        </span>
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">
                          {formatCardBrand(method.card?.brand || "card")} ••••{" "}
                          {method.card?.last4}
                        </p>
                        <p className="text-sm text-gray-500">
                          Expires{" "}
                          {method.card?.exp_month?.toString().padStart(2, "0")}/
                          {method.card?.exp_year}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                        Stripe
                      </span>
                      <button
                        onClick={() => detachPaymentMethod.mutate(method.id)}
                        disabled={isDetaching}
                        className="text-red-600 hover:text-red-800 text-sm disabled:opacity-50"
                      >
                        Remove
                      </button>
                    </div>
                  </div>
                </div>
              ))}

              {/* Legacy PaymentMethods */}
              {legacyPaymentMethods.length > 0
                ? legacyPaymentMethods.map((method: any) => (
                    <div
                      key={method.id}
                      className="border border-gray-200 rounded-lg p-4 relative"
                    >
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-3">
                          <div className="w-8 h-6 bg-blue-600 rounded flex items-center justify-center">
                            {method.type === "visa" ? (
                              <span className="text-white text-xs font-bold">
                                VISA
                              </span>
                            ) : (
                              <div className="w-4 h-4 bg-red-500 rounded-full"></div>
                            )}
                          </div>
                          <div>
                            <p className="font-medium text-gray-900">
                              {method.type === "visa" ? "Visa" : "Mastercard"}{" "}
                              ending in {method.last4}
                            </p>
                            <p className="text-sm text-gray-500">
                              Expires: {method.expires}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded">
                            Legacy
                          </span>
                          {!method.isDefault && (
                            <button
                              onClick={() =>
                                setDefaultPaymentMethod.mutate(method.id)
                              }
                              disabled={isSettingDefault}
                              className="text-blue-600 hover:text-blue-800 text-sm disabled:opacity-50"
                            >
                              Set Default
                            </button>
                          )}
                          <button
                            onClick={() =>
                              removeLegacyPaymentMethod.mutate(method.id)
                            }
                            disabled={isRemoving || method.isDefault}
                            className="text-red-600 hover:text-red-800 text-sm disabled:opacity-50"
                          >
                            Remove
                          </button>
                        </div>
                      </div>
                      {method.isDefault && (
                        <div className="flex items-center space-x-2 mt-2">
                          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                          <p className="text-sm text-green-600 font-medium">
                            Default payment method
                          </p>
                        </div>
                      )}
                    </div>
                  ))
                : null}

              {/* Show empty state only if no payment methods at all */}
              {stripePaymentMethods.length === 0 &&
                legacyPaymentMethods.length === 0 && (
                  <div className="col-span-2 text-center py-8">
                    <div className="text-gray-400 mb-4">
                      <svg
                        className="w-12 h-12 mx-auto"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"
                        />
                      </svg>
                    </div>
                    <p className="text-gray-500 mb-4">
                      No payment methods added yet
                    </p>
                    <InteractiveButton
                      onClick={() => setShowAddCardModal(true)}
                      className="bg-[#0F2C59] hover:bg-[#0F2C59]/90 text-white px-4 py-2 rounded-md text-sm"
                    >
                      Add Your First Card
                    </InteractiveButton>
                  </div>
                )}
            </div>
          </div>

          {/* Prepaid Account */}
          <div className="bg-white rounded-lg p-6">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-semibold text-gray-900">
                Prepaid Account
              </h3>
              <InteractiveButton
                onClick={() => setShowTopUpModal(true)}
                className="bg-[#0F2C59] hover:bg-[#0F2C59]/90 text-white px-4 py-2 rounded-md text-sm"
              >
                + Submit Top-up Request
              </InteractiveButton>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Current Balance */}
              <div>
                <div className="flex items-center space-x-2 mb-2">
                  <h4 className="text-sm font-medium text-gray-700">
                    Current Balance
                  </h4>
                  <div className="w-4 h-4 bg-gray-400 rounded-full flex items-center justify-center">
                    <span className="text-white text-xs">💰</span>
                  </div>
                </div>
                <p className="text-3xl font-bold text-gray-900">
                  ${accountBalance?.balance?.toFixed(2) || "0.00"}
                </p>
                <p className="text-sm text-gray-500">
                  Last topped up:{" "}
                  {accountBalance?.last_topped_up
                    ? new Date(
                        accountBalance.last_topped_up
                      ).toLocaleDateString()
                    : "Never"}
                </p>
              </div>

              {/* Top-up Methods */}
              <div>
                <div className="flex items-center space-x-2 mb-4">
                  <h4 className="text-sm font-medium text-gray-700">
                    Top-up Methods
                  </h4>
                  <div className="w-4 h-4 bg-gray-400 rounded-full flex items-center justify-center">
                    <span className="text-white text-xs">💳</span>
                  </div>
                </div>
                <div className="space-y-2">
                  {topUpMethods.map((method) => (
                    <div
                      key={method.id}
                      className="flex items-center space-x-2 text-sm"
                    >
                      <span>{method.icon}</span>
                      <span className="text-gray-700">{method.name}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Pending Requests */}
              <div>
                <div className="flex items-center space-x-2 mb-4">
                  <h4 className="text-sm font-medium text-gray-700">
                    Pending Requests
                  </h4>
                  <div className="w-4 h-4 bg-gray-400 rounded-full flex items-center justify-center">
                    <span className="text-white text-xs">ℹ</span>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="text-3xl font-bold text-gray-900">
                    {pendingRequests.length}
                  </div>
                  {pendingRequests.length > 0 ? (
                    pendingRequests.map((request) => (
                      <div
                        key={request.id}
                        className="border border-gray-200 rounded-lg p-3"
                      >
                        <div className="flex justify-between items-start mb-2">
                          <div>
                            <p className="font-medium text-gray-900">
                              ${request.amount}
                            </p>
                            <p className="text-sm text-gray-600">
                              via {request.method}
                            </p>
                          </div>
                          <button
                            onClick={() => handleCancelTopUpRequest(request.id)}
                            disabled={saving}
                            className="text-xs text-red-600 hover:text-red-800 disabled:opacity-50"
                          >
                            Cancel
                          </button>
                        </div>
                        <p className="text-xs text-gray-500">
                          Submitted on{" "}
                          {new Date(request.created_at).toLocaleDateString()}
                        </p>
                        <div className="mt-2">
                          <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                            Pending Review
                          </span>
                        </div>
                      </div>
                    ))
                  ) : (
                    <p className="text-sm text-gray-500">No pending requests</p>
                  )}
                </div>
              </div>
            </div>

            {/* e-Transfer Instructions */}
            <div className="mt-6 p-4 bg-gray-50 rounded-lg">
              <h4 className="text-sm font-medium text-gray-900 mb-2">
                e-Transfer Instructions
              </h4>
              <p className="text-sm text-gray-600 mb-1">
                Send e-transfer to: <EMAIL>
              </p>
              <p className="text-sm text-gray-600">
                Include your registered email in the message
              </p>
            </div>

            {/* Jamaica Wire Transfer Details */}
            <div className="mt-4 p-4 bg-gray-50 rounded-lg">
              <h4 className="text-sm font-medium text-gray-900 mb-2">
                Jamaica Wire Transfer Details
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-gray-600">
                    Bank: National Commercial Bank Jamaica
                  </p>
                  <p className="text-gray-600">
                    Account Name: eBa Platform Ltd
                  </p>
                </div>
                <div>
                  <p className="text-gray-600">Account Number: *********</p>
                  <p className="text-gray-600">Branch: Kingston Main</p>
                  <p className="text-gray-600">Swift Code: JNCBJMKX</p>
                </div>
              </div>
            </div>

            {/* Top-up Request History */}
            <div className="mt-6">
              <div className="flex justify-between items-center mb-4">
                <h4 className="text-lg font-semibold text-gray-900">
                  Top-up Request History
                </h4>
                <button
                  onClick={fetchAccountData}
                  disabled={loading}
                  className="text-sm text-blue-600 hover:text-blue-800 disabled:opacity-50"
                >
                  {loading ? "Refreshing..." : "Refresh"}
                </button>
              </div>

              {loading ? (
                <div className="text-center py-8">
                  <div className="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-[#0F2C59]"></div>
                  <p className="text-sm text-gray-600 mt-2">
                    Loading history...
                  </p>
                </div>
              ) : error ? (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                  <p className="text-sm text-red-600">{error}</p>
                  <button
                    onClick={fetchAccountData}
                    className="text-sm text-red-700 underline mt-1"
                  >
                    Try again
                  </button>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b border-gray-200">
                        <th className="text-left py-2 text-sm font-medium text-gray-700">
                          Date
                        </th>
                        <th className="text-left py-2 text-sm font-medium text-gray-700">
                          Payment Method
                        </th>
                        <th className="text-left py-2 text-sm font-medium text-gray-700">
                          Reference
                        </th>
                        <th className="text-left py-2 text-sm font-medium text-gray-700">
                          Amount
                        </th>
                        <th className="text-left py-2 text-sm font-medium text-gray-700">
                          Status
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {topUpHistory.length > 0 ? (
                        topUpHistory.map((item, index) => (
                          <tr key={index} className="border-b border-gray-100">
                            <td className="py-3 text-sm text-gray-900">
                              {item.date
                                ? new Date(item.date).toLocaleDateString()
                                : "N/A"}
                            </td>
                            <td className="py-3 text-sm text-gray-900">
                              {item.method || "N/A"}
                            </td>
                            <td className="py-3 text-sm text-gray-900">
                              {item.reference || "N/A"}
                            </td>
                            <td className="py-3 text-sm text-gray-900">
                              eBa${" "}
                              {typeof item.amount === "number"
                                ? item.amount.toFixed(2)
                                : item.amount || "0.00"}
                            </td>
                            <td className="py-3">
                              <span
                                className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                  item.status === "Approved" ||
                                  item.status === "approved"
                                    ? "bg-green-100 text-green-800"
                                    : item.status === "Pending" ||
                                        item.status === "pending"
                                      ? "bg-yellow-100 text-yellow-800"
                                      : item.status === "Rejected" ||
                                          item.status === "rejected"
                                        ? "bg-red-100 text-red-800"
                                        : "bg-gray-100 text-gray-800"
                                }`}
                              >
                                {item.status || "Unknown"}
                              </span>
                            </td>
                          </tr>
                        ))
                      ) : (
                        <tr>
                          <td
                            colSpan={5}
                            className="py-8 text-center text-gray-500"
                          >
                            <div className="flex flex-col items-center">
                              <svg
                                className="w-12 h-12 text-gray-300 mb-4"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                />
                              </svg>
                              <p className="text-sm font-medium text-gray-900 mb-1">
                                No top-up history found
                              </p>
                              <p className="text-xs text-gray-500">
                                Your top-up transactions will appear here
                              </p>
                            </div>
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </div>

          {/* eBa Dollars Exchange */}
          <div className="bg-white rounded-lg p-6">
            <h3 className="text-xl font-semibold text-gray-900 mb-6">
              EBA Dollars Exchange
            </h3>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Buy eBa Dollars */}
              <div>
                <div className="flex items-center space-x-2 mb-4">
                  <span className="text-green-600">💰</span>
                  <h4 className="text-lg font-semibold text-gray-900">
                    Buy EBA Dollars
                  </h4>
                </div>
                <p className="text-gray-600 text-sm mb-4">
                  Purchase EBA dollars directly from the platform using your
                  preferred payment method.
                </p>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Amount (EBA$)
                    </label>
                    <input
                      type="number"
                      placeholder="Enter amount"
                      value={topUpAmount}
                      onChange={(e) => setTopUpAmount(e.target.value)}
                      min="1"
                      step="0.01"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent"
                    />
                    {topUpAmount && (
                      <p className="text-sm text-gray-600 mt-1">
                        Total cost: $
                        {(parseFloat(topUpAmount || "0") * 1.45).toFixed(2)} USD
                        <span className="text-xs text-gray-500 ml-2">
                          (Rate: $1.45 per EBA$)
                        </span>
                      </p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Payment Method
                    </label>
                    <select
                      value={selectedPaymentMethod}
                      onChange={(e) => setSelectedPaymentMethod(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent"
                    >
                      <option value="">Select payment method</option>
                      {/* Stripe PaymentMethods */}
                      {stripePaymentMethods.map((method) => (
                        <option key={`stripe-${method.id}`} value={method.id}>
                          {formatCardBrand(method.card?.brand || "card")} ending
                          in {method.card?.last4} (Stripe)
                        </option>
                      ))}
                      {/* Legacy PaymentMethods */}
                      {legacyPaymentMethods.map((method: any) => (
                        <option key={`legacy-${method.id}`} value={method.id}>
                          {method.type === "visa" ? "Visa" : "Mastercard"}{" "}
                          ending in {method.last4} (Legacy)
                        </option>
                      ))}
                    </select>
                    {stripePaymentMethods.length === 0 &&
                      legacyPaymentMethods.length === 0 && (
                        <p className="text-sm text-gray-500 mt-1">
                          <button
                            onClick={() => setShowAddCardModal(true)}
                            className="text-[#0F2C59] hover:underline"
                          >
                            Add a payment method
                          </button>{" "}
                          to purchase EBA dollars
                        </p>
                      )}
                  </div>

                  <InteractiveButton
                    onClick={handleDirectPurchase}
                    loading={saving}
                    disabled={saving || !topUpAmount || !selectedPaymentMethod}
                    className="w-full bg-[#0F2C59] hover:bg-[#0F2C59]/90 text-white py-3 rounded-md disabled:opacity-50"
                  >
                    Purchase EBA Dollars
                  </InteractiveButton>
                </div>
              </div>

              {/* Peer-to-Peer Exchange */}
              <div>
                <div className="flex items-center space-x-2 mb-4">
                  <span className="text-blue-600">🔄</span>
                  <h4 className="text-lg font-semibold text-gray-900">
                    Peer-to-Peer Exchange
                  </h4>
                </div>
                <p className="text-gray-600 text-sm mb-4">
                  Buy or sell EBA dollars with other users in the community.
                </p>

                <div className="flex space-x-2 mb-4">
                  <InteractiveButton
                    onClick={() => setActiveExchangeTab("buy")}
                    className={`flex-1 py-2 rounded-md ${
                      activeExchangeTab === "buy"
                        ? "bg-[#0F2C59] hover:bg-[#0F2C59]/90 text-white"
                        : "border border-gray-300 text-gray-700 hover:bg-gray-50"
                    }`}
                  >
                    Buy EBA$
                  </InteractiveButton>
                  <InteractiveButton
                    onClick={() => setActiveExchangeTab("sell")}
                    className={`flex-1 py-2 rounded-md ${
                      activeExchangeTab === "sell"
                        ? "bg-[#0F2C59] hover:bg-[#0F2C59]/90 text-white"
                        : "border border-gray-300 text-gray-700 hover:bg-gray-50"
                    }`}
                  >
                    Sell EBA$
                  </InteractiveButton>
                </div>

                {activeExchangeTab === "buy" ? (
                  <div className="space-y-3">
                    {exchangeListings.filter(
                      (listing) => listing.type === "selling"
                    ).length > 0 ? (
                      exchangeListings
                        .filter((listing) => listing.type === "selling")
                        .map((listing) => (
                          <div
                            key={listing.id}
                            className="flex justify-between items-center p-3 border border-gray-200 rounded-lg"
                          >
                            <div>
                              <p className="font-medium text-gray-900">
                                Selling {listing.amount} EBA$
                              </p>
                              <p className="text-sm text-gray-600">
                                Rate: ${listing.rate} per EBA$
                              </p>
                              <p className="text-sm text-gray-500">
                                Total: ${listing.total_value}
                              </p>
                            </div>
                            <div className="flex gap-2">
                              <InteractiveButton
                                onClick={() => handleViewListing(listing)}
                                className="text-[#0F2C59] hover:underline text-sm"
                              >
                                View
                              </InteractiveButton>
                              <InteractiveButton
                                onClick={() =>
                                  handleBuyRequest(listing.id, listing.amount)
                                }
                                loading={saving}
                                disabled={saving}
                                className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm"
                              >
                                Buy
                              </InteractiveButton>
                            </div>
                          </div>
                        ))
                    ) : (
                      <div className="text-center py-6">
                        <p className="text-gray-500 mb-4">
                          No EBA$ available for purchase
                        </p>
                        <p className="text-sm text-gray-400">
                          Check back later or create a buy listing
                        </p>
                      </div>
                    )}

                    <InteractiveButton
                      onClick={() => setShowCreateListingModal(true)}
                      className="w-full text-[#0F2C59] hover:underline text-sm py-2"
                    >
                      + Create Buy Listing
                    </InteractiveButton>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {/* Your Listings */}
                    <div className="space-y-3">
                      <h5 className="text-sm font-medium text-gray-700">
                        Your Sell Listings
                      </h5>
                      {exchangeListings.filter(
                        (listing) => listing.type === "selling"
                      ).length > 0 ? (
                        exchangeListings
                          .filter((listing) => listing.type === "selling")
                          .map((listing) => (
                            <div
                              key={listing.id}
                              className="flex justify-between items-center p-3 border border-gray-200 rounded-lg"
                            >
                              <div>
                                <p className="font-medium text-gray-900">
                                  Selling {listing.amount} EBA$
                                </p>
                                <p className="text-sm text-gray-600">
                                  Rate: ${listing.rate} per EBA$
                                </p>
                                <p className="text-sm text-gray-600">
                                  Total: ${listing.total_value}
                                </p>
                              </div>
                              <div className="flex items-center gap-2">
                                <span
                                  className={`text-sm font-medium px-2 py-1 rounded-full ${
                                    listing.status === "active"
                                      ? "bg-green-100 text-green-700"
                                      : "bg-gray-100 text-gray-700"
                                  }`}
                                >
                                  {listing.status}
                                </span>
                                <button
                                  onClick={() =>
                                    handleDeleteListing(listing.id)
                                  }
                                  disabled={saving}
                                  className="text-red-600 hover:text-red-800 text-sm disabled:opacity-50"
                                >
                                  Delete
                                </button>
                              </div>
                            </div>
                          ))
                      ) : (
                        <p className="text-sm text-gray-500">
                          No sell listings yet
                        </p>
                      )}
                    </div>

                    {/* Buy Requests */}
                    <div className="space-y-3">
                      {buyRequests.map((request) => (
                        <div
                          key={request.id}
                          className="flex justify-between items-center p-3 border border-gray-200 rounded-lg"
                        >
                          <div>
                            <p className="font-medium text-gray-900">
                              {request.buyer_first_name} wants to buy{" "}
                              {request.amount} EBA$
                            </p>
                            <p className="text-sm text-gray-600">
                              Rate: ${request.rate} per EBA$
                            </p>
                            <p className="text-sm text-gray-600">
                              Total: ${request.total}
                            </p>
                          </div>
                          <div className="flex space-x-2">
                            <InteractiveButton className="text-[#0F2C59] hover:underline text-sm">
                              View Receipt
                            </InteractiveButton>
                            <InteractiveButton
                              onClick={() =>
                                handleApproveBuyRequest(request.id)
                              }
                              className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm"
                            >
                              Approve
                            </InteractiveButton>
                          </div>
                        </div>
                      ))}
                    </div>

                    <InteractiveButton
                      onClick={() => setShowCreateListingModal(true)}
                      className="w-full text-red-500 hover:underline text-sm py-2"
                    >
                      + Create New Listing
                    </InteractiveButton>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Deactivate Account */}
          <div className="bg-white rounded-lg p-6">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">
              Deactivate Account
            </h3>
            <p className="text-gray-600 text-sm mb-6">
              Deactivating your account will temporarily disable your profile
              and services until you log in again. All your data will be
              preserved.
            </p>
            <InteractiveButton className="bg-[#E63946] hover:bg-[#E63946]/90 text-white px-6 py-2 rounded-md">
              Deactivate Account
            </InteractiveButton>
          </div>
        </div>

        {/* Currency Converter Widget */}
        {/* <div className="fixed bottom-4 left-4 bg-white rounded-lg shadow-lg p-4 w-64 border border-gray-200">
          <h4 className="text-sm font-semibold text-gray-900 mb-3">
            Currency Converter
          </h4>
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <input
                type="number"
                placeholder="50"
                defaultValue="50"
                className="flex-1 px-2 py-1 border border-gray-300 rounded text-sm"
              />
              <span className="text-sm text-gray-600">eBa$</span>
            </div>
            <div className="flex items-center space-x-2">
              <select className="flex-1 px-2 py-1 border border-gray-300 rounded text-sm">
                <option>USD</option>
                <option>CAD</option>
                <option>JMD</option>
              </select>
              <span className="text-sm text-gray-600">= 72.50 USD</span>
            </div>
            <p className="text-xs text-gray-500">eBa$50 = 72.50 USD</p>
          </div>
        </div> */}

        {/* Inbox Widget */}
        <div className="fixed bottom-4 right-4 bg-white rounded-lg shadow-lg p-3 border border-gray-200">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
              <span className="text-xs">📧</span>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-900">Inbox 3</p>
              <div className="flex items-center space-x-1">
                <div className="w-6 h-6 rounded-full overflow-hidden">
                  <img
                    src={
                      profile?.photo ||
                      "https://images.unsplash.com/photo-*************-5658abf4ff4e?w=150&h=150&fit=crop&crop=face"
                    }
                    alt="Profile"
                    className="w-full h-full object-cover"
                  />
                </div>
                <span className="text-xs text-gray-600">
                  {profile?.first_name} {profile?.last_name}
                </span>
                <svg
                  className="w-3 h-3 text-gray-400"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Top-Up Request Modal */}
      <TopUpRequestModal
        isOpen={showTopUpModal}
        onClose={() => setShowTopUpModal(false)}
        onSubmit={handleTopUpRequest}
        onStripeSuccess={(result) => {
          console.log("Stripe top-up successful:", result);
          success(
            `Successfully added ${result.amount_added} EBA$ to your account!`
          );
          setShowTopUpModal(false);
          fetchAccountData(); // Refresh account data
        }}
      />

      {/* Add New Card Modal */}
      <StripePaymentModal
        isOpen={showAddCardModal}
        onClose={() => setShowAddCardModal(false)}
        onSuccess={(paymentMethod) => {
          console.log("Payment method added successfully:", paymentMethod);
          success("Payment method added successfully!");
          setShowAddCardModal(false);
          fetchAccountData(); // Refresh payment methods
        }}
      />

      {/* Create New Listing Modal */}
      <CreateNewListingModal
        isOpen={showCreateListingModal}
        onClose={() => setShowCreateListingModal(false)}
        onSubmit={handleCreateListing}
      />

      {/* View Listing Modal */}
      <ViewListingModal
        isOpen={showViewListingModal}
        onClose={() => setShowViewListingModal(false)}
        listing={selectedListing}
      />
    </MemberWrapper>
  );
};

export default MemberAccountPage;
