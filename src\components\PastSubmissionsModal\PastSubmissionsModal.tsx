import React from "react";
import { Modal } from "../Modal";
import { TrashIcon, XIcon } from "../../assets/svgs";
import { User } from "../../interfaces";

interface PastSubmissionsModalProps {
  isOpen: boolean;
  onClose: () => void;
  user: User | null;
  title: string;
}

const submissions = [
  {
    link: "https://www.amazon.com/kindle-library/download/book?asin=B08XXXXXXXXX",
  },
  {
    link: "https://www.amazon.com/kindle-library/download/book?asin=B08YYYYYYYYY",
  },
  {
    link: "https://www.amazon.com/kindle-library/download/book?asin=B08ZZZZZZZZZ",
  },
  {
    link: "https://www.amazon.com/kindle-library/download/book?asin=B08AAAAAAAAA",
  },
  {
    link: "https://www.amazon.com/kindle-library/download/book?asin=B08BBBBBBBBB",
  },
  {
    link: "https://www.amazon.com/kindle-library/download/book?asin=B08CCCCCCCCC",
  },
  {
    link: "https://www.amazon.com/kindle-library/download/book?asin=B08DDDDDDDDD",
  },
  {
    link: "https://www.amazon.com/kindle-library/download/book?asin=B08EEEEEEEEE",
  },
];

export const PastSubmissionsModal: React.FC<PastSubmissionsModalProps> = ({
  isOpen,
  onClose,
  user,
  title,
}) => {
  if (!user) return null;

  return (
    <Modal
      isOpen={isOpen}
      modalCloseClick={onClose}
      title=""
      modalHeader={false}
      classes={{
        modal: "h-full",
        modalDialog: "w-[90%] max-w-2xl bg-white !p-6 rounded-lg",
        modalContent: "!p-0 !m-0",
      }}
    >
      <div className="flex justify-between items-center mb-4">
        <div>
          <h2 className="text-xl font-bold text-[#1E293B]">{title}</h2>
          <p className="text-sm text-gray-500">All submissions</p>
        </div>
        <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
          <XIcon />
        </button>
      </div>
      <div className="flex items-center mb-6">
        <div className="w-12 h-12 rounded-full bg-gray-300 mr-4">
          {user.photo && (
            <img
              src={user.photo}
              alt={`${user.first_name} ${user.last_name}`}
              className="w-full h-full rounded-full object-cover"
            />
          )}
        </div>
        <div>
          <h3 className="text-lg font-semibold">{`${user.first_name} ${user.last_name}`}</h3>
          <p className="text-sm text-gray-500">
            Applied on: {new Date(user.joined).toLocaleDateString()}
          </p>
        </div>
      </div>
      <div className="overflow-x-auto">
        <table className="w-full text-sm text-left">
          <thead className="text-xs text-gray-700 uppercase bg-gray-50">
            <tr>
              <th scope="col" className="px-6 py-3">
                Link
              </th>
              <th scope="col" className="px-6 py-3 text-right">
                Actions
              </th>
            </tr>
          </thead>
          <tbody>
            {submissions.map((submission, index) => (
              <tr key={index} className="bg-white border-b">
                <td className="px-6 py-4 font-medium text-blue-600 whitespace-nowrap">
                  <a
                    href={submission.link}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    {submission.link}
                  </a>
                </td>
                <td className="px-6 py-4 flex items-center justify-end gap-3">
                  <button className="text-red-500 hover:text-red-700">
                    <TrashIcon />
                  </button>
                  <button className="text-blue-500 hover:text-blue-700">
                    <EyeIcon />
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </Modal>
  );
};

const EyeIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    className="h-5 w-5"
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth="2"
      d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
    />
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth="2"
      d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
    />
  </svg>
);
