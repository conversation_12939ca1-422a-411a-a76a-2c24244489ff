import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useSDK } from "../hooks/useSDK";
import { useToast } from "../hooks/useToast";
import { ToastStatusEnum } from "../utils/Enums";

// Interface definitions
interface IListing {
  id: number;
  name: string;
  description: string;
  listingType: string;
  category: string;
  price: number;
  discountPrice?: number;
  quantity: number;
  addressLine?: string;
  city?: string;
  country?: string;
  schedulingType?: string;
  serviceLocationType?: string;
  paymentTerms?: string;
  images: string[];
  status: string;
  dimensions?: any;
  shippingOptions?: any;
  sizes?: any[];
  availability?: any[];
  createdAt: string;
  updatedAt: string;
}

interface ICategory {
  id: number;
  name: string;
  value: string;
  label: string;
  status: string;
  created_at: string;
  updated_at: string;
}

interface IListingUpdateData {
  name: string;
  description: string;
  price: string | number;
  category: string;
  image?: string;
  status?: string;
  discountPrice?: string | number;
  quantity?: string | number;
  listingType?: string;
  addressLine?: string;
  city?: string;
  country?: string;
  dimensions?: any;
  shippingOptions?: any;
  sizes?: any[];
  schedulingType?: string;
  serviceLocationType?: string;
  paymentTerms?: string;
  availability?: any[];
}

// Hook to fetch single listing
export const useListingQuery = (id: string) => {
  const { sdk } = useSDK();

  return useQuery({
    queryKey: ["listing", id],
    queryFn: async () => {
      console.log("🚀 Fetching listing with ID:", id);

      try {
        const response = await sdk.request({
          endpoint: `/v2/api/ebadollar/member/listings/${id}`,
          method: "GET",
        });
        console.log("📡 Listing API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Listing API Call Error:", error);
        throw error;
      }
    },
    enabled: !!id && !isNaN(Number(id)),
  });
};

// Hook to update listing
export const useUpdateListingMutation = () => {
  const { sdk } = useSDK();
  const queryClient = useQueryClient();
  const { showToast } = useToast();

  return useMutation({
    mutationFn: async ({
      id,
      data,
    }: {
      id: string;
      data: IListingUpdateData;
    }) => {
      console.log("🚀 Updating listing with ID:", id, "data:", data);

      try {
        const response = await sdk.request({
          endpoint: `/v2/api/ebadollar/member/listings/${id}`,
          method: "PUT",
          body: data,
        });
        console.log("📡 Update Listing API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Update Listing API Call Error:", error);
        throw error;
      }
    },
    onSuccess: (res: any, variables) => {
      console.log("✅ Update Listing Mutation Success:", res);
      if (!res.error) {
        showToast(
          "Listing updated successfully",
          5000,
          ToastStatusEnum.SUCCESS
        );
        // Invalidate related queries
        queryClient.invalidateQueries({ queryKey: ["listing", variables.id] });
        queryClient.invalidateQueries({ queryKey: ["listings"] });
      } else {
        console.error("❌ API returned error:", res.message);
        showToast(
          res.message || "Failed to update listing",
          5000,
          ToastStatusEnum.ERROR
        );
      }
    },
    onError: (err: any) => {
      console.error("❌ Update Listing Mutation Error:", err);
      showToast(
        err.message || "An error occurred while updating the listing",
        5000,
        ToastStatusEnum.ERROR
      );
    },
  });
};

// Hook to delete listing
export const useDeleteListingMutation = () => {
  const { sdk } = useSDK();
  const queryClient = useQueryClient();
  const { showToast } = useToast();

  return useMutation({
    mutationFn: async (id: string) => {
      console.log("🚀 Deleting listing with ID:", id);

      try {
        const response = await sdk.request({
          endpoint: `/v2/api/ebadollar/member/listings/${id}`,
          method: "DELETE",
        });
        console.log("📡 Delete Listing API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Delete Listing API Call Error:", error);
        throw error;
      }
    },
    onSuccess: (res: any) => {
      console.log("✅ Delete Listing Mutation Success:", res);
      if (!res.error) {
        showToast(
          "Listing deleted successfully",
          5000,
          ToastStatusEnum.SUCCESS
        );
        // Invalidate listings queries
        queryClient.invalidateQueries({ queryKey: ["listings"] });
      } else {
        console.error("❌ API returned error:", res.message);
        showToast(
          res.message || "Failed to delete listing",
          5000,
          ToastStatusEnum.ERROR
        );
      }
    },
    onError: (err: any) => {
      console.error("❌ Delete Listing Mutation Error:", err);
      showToast(
        err.message || "An error occurred while deleting the listing",
        5000,
        ToastStatusEnum.ERROR
      );
    },
  });
};

// Hook to fetch categories for members
export const useMemberCategoriesQuery = () => {
  const { sdk } = useSDK();

  return useQuery({
    queryKey: ["member-categories"],
    queryFn: async () => {
      console.log("🚀 Fetching member categories");

      try {
        const response = await sdk.request({
          endpoint: "/v2/api/ebadollar/custom/member/categories",
          method: "GET",
        });
        console.log("📡 Member Categories API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Member Categories API Call Error:", error);
        throw error;
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Hook to fetch member's listings
export const useMemberListingsQuery = (params?: {
  page?: number;
  limit?: number;
  status?: string;
}) => {
  const { sdk } = useSDK();

  return useQuery({
    queryKey: ["member-listings", params],
    queryFn: async () => {
      console.log("🚀 Fetching member listings with params:", params);

      try {
        const response = await sdk.request({
          endpoint: "/v2/api/ebadollar/member/listings",
          method: "GET",
          params: params,
        });
        console.log("📡 Member Listings API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Member Listings API Call Error:", error);
        throw error;
      }
    },
  });
};

export type { IListing, ICategory, IListingUpdateData };
