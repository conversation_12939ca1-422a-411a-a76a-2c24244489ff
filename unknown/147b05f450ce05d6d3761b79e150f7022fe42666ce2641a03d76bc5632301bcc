import { useState, useEffect } from "react";
import { useSDK } from "../../../hooks/useSDK";
import { Skeleton } from "../../../components/Skeleton";
import { useToast } from "../../../components/Toast";

interface Document {
  id: number;
  document_type: string;
  document_url: string;
  status: string;
  admin_remark?: string;
  created_at: string;
  updated_at: string;
}

interface ApplicationData {
  id: number;
  user_id: number;
  status: string;
  created_at: string;
  updated_at: string;
  user_email?: string;
  user_data?: any;
}

interface DeliveryVerifyDocumentsModalProps {
  onClose: () => void;
  applicationId: number;
}

const DeliveryVerifyDocumentsModal = ({
  onClose,
  applicationId,
}: DeliveryVerifyDocumentsModalProps) => {
  const { sdk } = useSDK();
  const toast = useToast();
  const [documents, setDocuments] = useState<Document[]>([]);
  const [applicationData, setApplicationData] =
    useState<ApplicationData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch documents
        const docsResponse = await sdk.request({
          endpoint: `/v2/api/ebadollar/custom/admin/delivery-applications/${applicationId}/documents`,
          method: "GET",
        });

        // Fetch application info
        const appResponse = await sdk.request({
          endpoint: `/v2/api/ebadollar/custom/admin/delivery-applications/${applicationId}`,
          method: "GET",
        });

        if (!docsResponse.error) {
          setDocuments(docsResponse.data || []);
        }

        if (!appResponse.error) {
          setApplicationData(appResponse.data || null);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
        toast.error("Failed to fetch application data");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [applicationId, toast]);

  const updateDocumentStatus = async (docId: number, status: string) => {
    try {
      const response = await sdk.request({
        endpoint: `/v2/api/ebadollar/custom/admin/delivery-documents/${docId}`,
        method: "PUT",
        body: { status },
      });

      if (!response.error) {
        // Update local state
        setDocuments((prev) =>
          prev.map((doc) => (doc.id === docId ? { ...doc, status } : doc))
        );
        toast.success(`Document ${status} successfully`);
      } else {
        toast.error(response.message || "Failed to update document status");
      }
    } catch (error) {
      console.error("Error updating document status:", error);
      toast.error("Error updating document status");
    }
  };

  const getDocumentIcon = (docType: string) => {
    const iconClass = "w-8 h-8 text-gray-400";

    switch (docType.toLowerCase()) {
      case "government id verification":
      case "government_id":
        return (
          <svg className={iconClass} fill="currentColor" viewBox="0 0 20 20">
            <path d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 2h8v2H6V6zm0 4h8v2H6v-2z" />
          </svg>
        );
      case "driver's license":
      case "drivers_license":
        return (
          <svg className={iconClass} fill="currentColor" viewBox="0 0 20 20">
            <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" />
            <path
              fillRule="evenodd"
              d="M4 5a2 2 0 012-2v1a1 1 0 102 0V3h4v1a1 1 0 102 0V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3z"
              clipRule="evenodd"
            />
          </svg>
        );
      case "insurance document":
      case "insurance":
        return (
          <svg className={iconClass} fill="currentColor" viewBox="0 0 20 20">
            <path
              fillRule="evenodd"
              d="M3 6a3 3 0 013-3h10a1 1 0 01.8 1.6L14.25 8l2.55 3.4A1 1 0 0116 13H6a1 1 0 00-1 1v3a1 1 0 11-2 0V6z"
              clipRule="evenodd"
            />
          </svg>
        );
      default:
        return (
          <svg className={iconClass} fill="currentColor" viewBox="0 0 20 20">
            <path
              fillRule="evenodd"
              d="M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 01-1.414 1.414L5 6.414V8a1 1 0 01-2 0V4zm9 1a1 1 0 010-2h4a1 1 0 011 1v4a1 1 0 01-2 0V6.414l-2.293 2.293a1 1 0 11-1.414-1.414L13.586 5H12zm-9 7a1 1 0 012 0v1.586l2.293-2.293a1 1 0 111.414 1.414L6.414 15H8a1 1 0 010 2H4a1 1 0 01-1-1v-4zm13-1a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 010-2h1.586l-2.293-2.293a1 1 0 111.414-1.414L15.586 13H14a1 1 0 01-1-1z"
              clipRule="evenodd"
            />
          </svg>
        );
    }
  };

  const renderDocumentCard = (doc: Document, title: string) => (
    <div key={doc.id} className="text-center">
      <h4 className="text-sm font-medium text-gray-900 mb-3">{title}</h4>

      {/* Document Preview Card */}
      <div className="bg-gray-600 rounded-lg p-8 mb-3 flex items-center justify-center min-h-[120px]">
        {doc.document_url ? (
          <img
            src={doc.document_url}
            alt={title}
            className="max-w-full max-h-full object-contain rounded"
            onError={(e) => {
              // Fallback to icon if image fails to load
              e.currentTarget.style.display = "none";
              e.currentTarget.nextElementSibling?.classList.remove("hidden");
            }}
          />
        ) : null}
        <div className={doc.document_url ? "hidden" : ""}>
          {getDocumentIcon(doc.document_type)}
        </div>
      </div>

      {/* Action Buttons */}
      {doc.document_url && (
        <div className="flex justify-center space-x-2 mb-3">
          <button
            onClick={() => {
              const link = document.createElement("a");
              link.href = doc.document_url;
              link.download = `${title.replace(/\s+/g, "_")}_${doc.id}`;
              link.target = "_blank";
              link.rel = "noopener noreferrer";
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);
            }}
            className="flex items-center text-xs bg-blue-50 text-blue-600 hover:bg-blue-100 hover:text-blue-700 px-3 py-1.5 rounded-md border border-blue-200 transition-colors"
          >
            <svg
              className="w-4 h-4 mr-1"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
            Download
          </button>
          <button
            onClick={() => {
              window.open(doc.document_url, "_blank", "noopener,noreferrer");
            }}
            className="flex items-center text-xs bg-gray-50 text-gray-600 hover:bg-gray-100 hover:text-gray-700 px-3 py-1.5 rounded-md border border-gray-200 transition-colors"
          >
            <svg
              className="w-4 h-4 mr-1"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
              <path
                fillRule="evenodd"
                d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                clipRule="evenodd"
              />
            </svg>
            Preview
          </button>
        </div>
      )}

      {/* Status Display and Action Buttons */}
      <div className="space-y-2">
        {doc.document_url ? (
          <>
            {/* Current Status */}
            {doc.status === "approved" && (
              <div className="w-full py-2 px-4 rounded text-sm font-medium bg-green-100 text-green-800">
                ✓ Approved
              </div>
            )}
            {doc.status === "rejected" && (
              <div className="w-full py-2 px-4 rounded text-sm font-medium bg-red-100 text-red-800">
                ✗ Rejected
              </div>
            )}
            {doc.status === "verified" && (
              <div className="w-full py-2 px-4 rounded text-sm font-medium bg-green-100 text-green-800">
                ✓ Verified
              </div>
            )}

            {/* Action Buttons for pending documents */}
            {doc.status === "pending" && (
              <>
                <button
                  onClick={() => updateDocumentStatus(doc.id, "approved")}
                  className="w-full py-2 px-4 rounded text-sm font-medium bg-green-500 text-white hover:bg-green-600"
                >
                  <svg
                    className="w-4 h-4 inline mr-1"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                  Approve document
                </button>
                <button
                  onClick={() => updateDocumentStatus(doc.id, "rejected")}
                  className="w-full py-2 px-4 rounded text-sm font-medium bg-red-500 text-white hover:bg-red-600"
                >
                  <svg
                    className="w-4 h-4 inline mr-1"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                      clipRule="evenodd"
                    />
                  </svg>
                  Reject
                </button>
              </>
            )}
          </>
        ) : (
          <div className="w-full py-2 px-4 rounded text-sm font-medium bg-gray-100 text-gray-600">
            No document submitted
          </div>
        )}
      </div>
    </div>
  );

  // Define document types and their display names based on backend data
  const identityDocs = [
    { type: "drivers_license", title: "Driver's License" },
    { type: "government_id", title: "Government ID" },
    { type: "proof_of_address", title: "Proof of Address" },
  ];

  const vehicleDocs = [
    { type: "vehicle_registration", title: "Vehicle Registration" },
    { type: "insurance_certificate", title: "Insurance Certificate" },
    { type: "vehicle_inspection", title: "Vehicle Inspection" },
    { type: "vehicle_photos", title: "Vehicle Photos" },
  ];

  if (loading) {
    return (
      <div className="bg-white rounded-lg p-6 w-full max-w-4xl mx-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-lg font-medium text-gray-900">
            Verify documents
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 text-xl font-medium"
          >
            ×
          </button>
        </div>
        <Skeleton className="h-96 w-full" />
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg p-6 w-full max-w-4xl mx-auto max-h-[90vh] overflow-y-auto">
      {/* Header */}
      <div className="flex justify-between items-center mb-6 sticky top-0 bg-white z-10 pb-2">
        <h2 className="text-lg font-medium text-gray-900">Verify documents</h2>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-gray-600 text-xl font-medium"
        >
          ×
        </button>
      </div>

      {/* User Info */}
      <div className="flex items-center mb-6">
        <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-medium mr-3">
          {applicationData?.user_email?.charAt(0)?.toUpperCase() || "U"}
        </div>
        <div>
          <div className="flex items-center">
            <span className="text-sm font-medium text-gray-900 mr-2">
              👤 User: {applicationData?.user_email || "Unknown User"}
            </span>
          </div>
          <div className="text-xs text-gray-500">
            Applied on:{" "}
            {applicationData?.created_at
              ? new Date(applicationData.created_at).toLocaleDateString()
              : "Unknown Date"}
          </div>
        </div>
      </div>

      {/* Identity Documents Section */}
      <div className="mb-8">
        <div className="flex items-center mb-4">
          <span className="text-blue-500 mr-2">🆔</span>
          <h3 className="text-sm font-medium text-gray-900">
            Identity Documents
          </h3>
        </div>
        <div className="grid grid-cols-3 gap-6">
          {identityDocs.map((docType) => {
            const doc = documents.find(
              (d) =>
                d.document_type
                  .toLowerCase()
                  .includes(docType.type.toLowerCase()) ||
                docType.type
                  .toLowerCase()
                  .includes(d.document_type.toLowerCase())
            ) || {
              id: Math.random(),
              document_type: docType.type,
              document_url: "",
              status: "pending",
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
            };
            return renderDocumentCard(doc, docType.title);
          })}
        </div>
      </div>

      {/* Vehicle Documents Section */}
      <div>
        <div className="flex items-center mb-4">
          <span className="text-red-500 mr-2">🚗</span>
          <h3 className="text-sm font-medium text-gray-900">
            Vehicle Documents
          </h3>
        </div>
        <div className="grid grid-cols-4 gap-4">
          {vehicleDocs.map((docType) => {
            const doc = documents.find(
              (d) =>
                d.document_type
                  .toLowerCase()
                  .includes(docType.type.toLowerCase()) ||
                docType.type
                  .toLowerCase()
                  .includes(d.document_type.toLowerCase())
            ) || {
              id: Math.random(),
              document_type: docType.type,
              document_url: "",
              status: "pending",
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
            };
            return renderDocumentCard(doc, docType.title);
          })}
        </div>
      </div>

      {/* Other Documents Section */}
      {(() => {
        const allDefinedTypes = [...identityDocs, ...vehicleDocs].map((d) =>
          d.type.toLowerCase()
        );
        const otherDocs = documents.filter(
          (doc) =>
            !allDefinedTypes.some(
              (type) =>
                doc.document_type.toLowerCase().includes(type) ||
                type.includes(doc.document_type.toLowerCase())
            )
        );

        if (otherDocs.length === 0) return null;

        return (
          <div className="mt-8">
            <div className="flex items-center mb-4">
              <span className="text-purple-500 mr-2">📄</span>
              <h3 className="text-sm font-medium text-gray-900">
                Other Documents
              </h3>
            </div>
            <div className="grid grid-cols-3 gap-6">
              {otherDocs.map((doc) =>
                renderDocumentCard(
                  doc,
                  doc.document_type
                    .replace(/_/g, " ")
                    .replace(/\b\w/g, (l) => l.toUpperCase())
                )
              )}
            </div>
          </div>
        );
      })()}
    </div>
  );
};

export default DeliveryVerifyDocumentsModal;
